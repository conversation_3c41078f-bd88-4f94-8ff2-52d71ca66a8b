const Mock = require("mockjs");

const data = Mock.mock({
  totalMovies: 150,
  totalTVShows: 80,
  totalVarietyShows: 45,
  totalUsers: 2500,
  totalViews: 1250000,
  monthlyNewContent: 25,
  averageRating: 7.8,
  topGenres: [
    { genre: "剧情", count: 45, percentage: 18.0 },
    { genre: "动作", count: 38, percentage: 15.2 },
    { genre: "喜剧", count: 32, percentage: 12.8 },
    { genre: "爱情", count: 28, percentage: 11.2 },
    { genre: "科幻", count: 25, percentage: 10.0 },
  ],
});

module.exports = [
  {
    url: "/vue-admin-template/stats",
    type: "get",
    response: (config) => {
      return {
        code: 200,
        message: "success",
        data: data,
      };
    },
  },
];
