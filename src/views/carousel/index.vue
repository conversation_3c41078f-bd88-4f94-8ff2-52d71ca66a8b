<template>
  <div class="carousel-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h2>轮播图管理</h2>
          <p>管理网站首页轮播图的增删改查</p>
        </div>
        <div class="header-actions">
          <el-button icon="el-icon-refresh" @click="fetchCarouselList">刷新</el-button>
          <el-button icon="el-icon-connection" @click="testConnection">测试连接</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            新增轮播图
          </el-button>
        </div>
      </div>
    </div>

    <!-- 轮播图列表 -->
    <div class="carousel-list">
      <el-table
        v-loading="loading"
        :data="carouselList"
        style="width: 100%"
        row-key="id"
        :default-sort="{prop: 'sortOrder', order: 'ascending'}"
      >
        <template slot="empty">
          <div class="empty-state">
            <i class="el-icon-picture-outline" style="font-size: 48px; color: #ddd;"></i>
            <p>暂无轮播图数据</p>
            <el-button type="primary" size="small" @click="handleAdd">添加第一个轮播图</el-button>
          </div>
        </template>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="图片" width="120">
          <template slot-scope="scope">
            <img
              :src="scope.row.imageUrl"
              :alt="scope.row.title"
              class="carousel-thumb"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="linkUrl" label="跳转链接" min-width="150" show-overflow-tooltip />
        <el-table-column prop="sortOrder" label="排序" width="80" sortable />
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="carouselForm"
        :model="carouselForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="carouselForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="carouselForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入轮播图描述"
          />
        </el-form-item>
        <el-form-item label="图片URL" prop="imageUrl">
          <el-input v-model="carouselForm.imageUrl" placeholder="请输入图片URL" />
          <div v-if="carouselForm.imageUrl" class="image-preview">
            <img :src="carouselForm.imageUrl" alt="预览图" @error="handleImageError" />
          </div>
        </el-form-item>
        <el-form-item label="跳转链接" prop="linkUrl">
          <el-input v-model="carouselForm.linkUrl" placeholder="请输入跳转链接" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="carouselForm.sortOrder"
            :min="0"
            :max="999"
            placeholder="排序数字"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="carouselForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="轮播图详情"
      :visible.sync="viewDialogVisible"
      width="500px"
    >
      <div v-if="currentCarousel" class="carousel-detail">
        <div class="detail-image">
          <img :src="currentCarousel.imageUrl" :alt="currentCarousel.title" />
        </div>
        <div class="detail-info">
          <p><strong>ID:</strong> {{ currentCarousel.id }}</p>
          <p><strong>标题:</strong> {{ currentCarousel.title }}</p>
          <p><strong>描述:</strong> {{ currentCarousel.description }}</p>
          <p><strong>跳转链接:</strong> {{ currentCarousel.linkUrl }}</p>
          <p><strong>排序:</strong> {{ currentCarousel.sortOrder }}</p>
          <p><strong>状态:</strong>
            <el-tag :type="currentCarousel.status === 1 ? 'success' : 'danger'">
              {{ currentCarousel.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </p>
          <p><strong>创建时间:</strong> {{ formatDate(currentCarousel.createdAt) }}</p>
          <p><strong>更新时间:</strong> {{ formatDate(currentCarousel.updatedAt) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCarouselList, getCarouselDetail, createCarousel, updateCarousel, deleteCarousel } from '@/api/carousel'

export default {
  name: 'CarouselManagement',
  data() {
    return {
      loading: false,
      carouselList: [],
      dialogVisible: false,
      viewDialogVisible: false,
      submitLoading: false,
      isEdit: false,
      currentCarousel: null,
      carouselForm: {
        title: '',
        description: '',
        imageUrl: '',
        linkUrl: '',
        sortOrder: 0,
        status: 1
      },
      formRules: {
        title: [
          { required: true, message: '请输入轮播图标题', trigger: 'blur' },
          { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        imageUrl: [
          { required: true, message: '请输入图片URL', trigger: 'blur' },
          { pattern: /^https?:\/\/.+/, message: '请输入正确的URL格式（以http://或https://开头）', trigger: 'blur' }
        ],
        linkUrl: [
          { pattern: /^https?:\/\/.+/, message: '请输入正确的URL格式（以http://或https://开头）', trigger: 'blur' }
        ],
        description: [
          { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
        ],
        sortOrder: [
          { type: 'number', message: '排序必须为数字', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑轮播图' : '新增轮播图'
    }
  },
  mounted() {
    this.fetchCarouselList()
  },
  methods: {
    // 获取轮播图列表
    async fetchCarouselList() {
      this.loading = true
      try {
        const response = await getCarouselList()
        console.log('Carousel List Response:', response)
        if (response.code === 200) {
          this.carouselList = response.data || []
        } else {
          this.$message.error(response.message || '获取轮播图列表失败')
        }
      } catch (error) {
        console.error('获取轮播图列表失败:', error)
        this.$message.error('获取轮播图列表失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    // 新增轮播图
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
    },
    // 编辑轮播图
    handleEdit(row) {
      this.isEdit = true
      this.currentCarousel = row
      this.carouselForm = {
        title: row.title,
        description: row.description,
        imageUrl: row.imageUrl,
        linkUrl: row.linkUrl,
        sortOrder: row.sortOrder,
        status: row.status
      }
      this.dialogVisible = true
    },
    // 查看轮播图详情
    async handleView(row) {
      try {
        const response = await getCarouselDetail(row.id)
        if (response.code === 200) {
          this.currentCarousel = response.data
        } else {
          this.$message.error(response.message || '获取轮播图详情失败')
          this.currentCarousel = row
        }
        this.viewDialogVisible = true
      } catch (error) {
        console.error('获取轮播图详情失败:', error)
        this.$message.error('获取轮播图详情失败，请稍后重试')
        // 如果获取详情失败，使用列表中的数据
        this.currentCarousel = row
        this.viewDialogVisible = true
      }
    },
    // 删除轮播图
    handleDelete(row) {
      this.$confirm('确定要删除这个轮播图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteCarousel(row.id)
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.fetchCarouselList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除轮播图失败:', error)
          this.$message.error('删除失败，请稍后重试')
        }
      })
    },
    // 提交表单
    async handleSubmit() {
      this.$refs.carouselForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            console.log('提交的表单数据:', this.carouselForm)

            // 转换数据格式以匹配后端接口
            const submitData = {
              title: this.carouselForm.title,
              imageUrl: this.carouselForm.imageUrl,
              linkUrl: this.carouselForm.linkUrl || '',
              description: this.carouselForm.description || '',
              sortOrder: this.carouselForm.sortOrder || 0,
              status: this.carouselForm.status
            }

            // 移除空值字段
            Object.keys(submitData).forEach(key => {
              if (submitData[key] === '' && key !== 'title' && key !== 'imageUrl') {
                delete submitData[key]
              }
            })

            console.log('转换后的提交数据:', submitData)

            let response
            if (this.isEdit) {
              response = await updateCarousel(this.currentCarousel.id, submitData)
            } else {
              response = await createCarousel(submitData)
            }

            if (response.code === 200) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.fetchCarouselList()
            } else {
              this.$message.error(response.message || '操作失败')
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error('操作失败，请稍后重试')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    // 重置表单
    resetForm() {
      this.carouselForm = {
        title: '',
        description: '',
        imageUrl: '',
        linkUrl: '',
        sortOrder: 0,
        status: 1
      }
      this.$refs.carouselForm && this.$refs.carouselForm.resetFields()
    },
    // 状态切换
    async handleStatusChange(row) {
      try {
        const response = await updateCarousel(row.id, { status: row.status })
        if (response.code === 200) {
          this.$message.success('状态更新成功')
        } else {
          // 如果更新失败，恢复原状态
          row.status = row.status === 1 ? 0 : 1
          this.$message.error(response.message || '状态更新失败')
        }
      } catch (error) {
        // 如果更新失败，恢复原状态
        row.status = row.status === 1 ? 0 : 1
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败，请稍后重试')
      }
    },
    // 图片加载错误处理
    handleImageError(event) {
      event.target.style.display = 'none'
      this.$message.warning('图片加载失败，请检查URL是否正确')
    },
    // 测试连接
    async testConnection() {
      try {
        console.log('测试连接到后端...')
        const response = await fetch('http://localhost:8080/carousel')
        console.log('连接测试响应状态:', response.status)
        const data = await response.json()
        console.log('连接测试响应数据:', data)
        if (response.ok) {
          this.$message.success('后端连接正常')
        } else {
          this.$message.error(`后端连接异常: ${response.status}`)
        }
      } catch (error) {
        console.error('连接测试失败:', error)
        this.$message.error('无法连接到后端服务，请检查后端是否启动')
      }
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.carousel-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
}

.carousel-list {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .carousel-thumb {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #eee;
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
    color: #999;

    p {
      margin: 15px 0;
      font-size: 14px;
    }
  }
}

.carousel-detail {
  .detail-image {
    text-align: center;
    margin-bottom: 20px;

    img {
      max-width: 100%;
      max-height: 200px;
      border-radius: 8px;
      border: 1px solid #eee;
    }
  }

  .detail-info {
    p {
      margin: 10px 0;
      font-size: 14px;
      line-height: 1.6;

      strong {
        display: inline-block;
        width: 80px;
        color: #333;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

.image-preview {
  margin-top: 10px;
  text-align: center;

  img {
    max-width: 200px;
    max-height: 120px;
    border-radius: 4px;
    border: 1px solid #eee;
    object-fit: cover;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .carousel-container {
    padding: 10px;

    .page-header {
      padding: 20px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        h2 {
          font-size: 20px;
        }
      }
    }

    .carousel-list {
      padding: 15px;
      overflow-x: auto;
    }
  }
}
</style>
